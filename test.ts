import { Pool } from 'undici'
import { parseUrl } from './app/utils/urls'

const { origin, path } = parseUrl('https://ewr1.nozomi.temporal.xyz/ping')

const pool = new Pool(origin, {
    connections: null,
    clientTtl: null,
    pipelining: 1,
    keepAliveTimeout: 65_000,
    keepAliveMaxTimeout: 65_000,
})

pool.on('connect', () => {
    console.log(new Date().toISOString(), 'Connect')
})

pool.on('disconnect', (_, __, error) => {
    console.log(new Date().toISOString(), 'Disconnect', error)
})

const response = await pool.request({ origin, path, method: 'GET' })

console.log('status', response.statusCode)
console.log('headers', response.headers)
console.log('body', await response.body.text())

setTimeout(() => process.exit(), 10_000)
