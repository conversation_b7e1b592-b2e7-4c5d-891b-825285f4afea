import type { UrlLike } from '@kdt310722/rpc'
import type { SenderOptions, WarmupOptions } from './sender'
import { map } from '@kdt310722/utils/object'
import { appendQueryParams } from '../../../utils/urls'
import { TEMPORAL_ENDPOINTS, TEMPORAL_MINIMUM_TIP_AMOUNT, TEMPORAL_TIP_ACCOUNTS } from '../constants'
import { RpcSender } from './rpc'

export interface TemporalSenderOptions<TEndpoints extends Record<string, UrlLike> = typeof TEMPORAL_ENDPOINTS> extends SenderOptions {
    endpoints?: TEndpoints
}

export const DEFAULT_TEMPORAL_WARMUP_OPTIONS: WarmupOptions = {
    interval: 60_000,
    method: 'GET',
    path: '/ping',
}

export const formatEndpoints = (endpoints: Record<string, UrlLike>, apiKey: string, queryParams = 'c'): Record<string, UrlLike> => map(endpoints, (key, value) => {
    return [key, appendQueryParams(value, { [queryParams]: apiKey })]
})

export class TemporalSender<TEndpoints extends Record<string, UrlLike> = typeof TEMPORAL_ENDPOINTS> extends RpcSender {
    public override readonly name = 'Temporal'
    public override readonly tipWallets = TEMPORAL_TIP_ACCOUNTS
    public override readonly minimumTipAmount = TEMPORAL_MINIMUM_TIP_AMOUNT

    public constructor(endpoint: Extract<keyof TEndpoints, string>, apiKey: string, { endpoints, warmup = {}, ...options }: TemporalSenderOptions<TEndpoints> = {}) {
        super(formatEndpoints(endpoints ?? TEMPORAL_ENDPOINTS, apiKey), endpoint, { ...options, warmup: { ...DEFAULT_TEMPORAL_WARMUP_OPTIONS, ...warmup } })
    }
}
