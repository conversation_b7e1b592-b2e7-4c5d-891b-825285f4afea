import type { Url<PERSON>ike } from '@kdt310722/rpc'
import type { ErrorOptions } from 'zod-validation-error'
import type { SendStats } from '../types'
import { isNullish, notNullish, type Nullable } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import { tap, transform, tryCatch } from '@kdt310722/utils/function'
import { map, pick, resolveNestedOptions } from '@kdt310722/utils/object'
import { createDeferred, type RetryOptions, withRetry, withTimeout } from '@kdt310722/utils/promise'
import { getAddMemoInstruction } from '@solana-program/memo'
import { type Address, appendTransactionMessageInstructions, type Base64EncodedWireTransaction, type BaseTransactionMessage, pipe, type Signature, type TransactionSigner } from '@solana/kit'
import { type Dispatcher, interceptors, Pool } from 'undici'
import { parseUrl } from '../../../utils/urls'
import { getTransferSolToRandomWalletInstruction } from '../instructions'

export interface SendOptions extends Partial<Omit<Dispatcher.RequestOptions, 'headers' | 'origin'>> {
    timeout?: number
    retry?: RetryOptions & { enabled?: boolean }
    headers?: Record<string, string>
}

export interface WarmupOptions {
    enabled?: boolean
    connections?: number
    interval?: number
    path?: string
    method?: string
    headers?: Record<string, string>
    body?: Nullable<string>
}

export interface SenderOptions {
    retry?: (RetryOptions & { enabled?: boolean }) | boolean
    timeout?: number
    headers?: Record<string, string>
    copyright?: boolean
    primaryOnly?: boolean
    pool?: Pool.Options
    dns?: interceptors.DNSInterceptorOpts & { enabled?: boolean }
    warmup?: WarmupOptions
}

export interface FailedAttemptError extends Error {
    readonly attemptNumber: number
    readonly retriesLeft: number
    readonly request: { url: string, method: string, headers: Record<string, string>, body: unknown }
}

export type SenderEvents = {
    beforeRequest: (endpoint: string, freeConnections: number) => void
    request: (endpoint: string, requestId: number, stats: SendStats) => void
    retry: (error: FailedAttemptError) => void
    warmingUp: (connections: number, fromInterval: boolean) => void
    warmedUp: (connections: Record<string, number>, fromInterval: boolean) => void
    warmupFailed: (error: unknown) => void
    connections: (endpoint: string, count: number) => void
}

export interface SenderEndpoint {
    name: string
    url: string
    origin: string
    path: string
    pool: Pool
}

export abstract class Sender<TEndpoints extends Record<string, UrlLike> = Record<string, UrlLike>> extends Emitter<SenderEvents> {
    public abstract readonly name: string
    public abstract readonly tipWallets: Address[]
    public abstract readonly minimumTipAmount: bigint

    protected readonly warmupOptions: WarmupOptions
    protected readonly headers: Record<string, string>
    protected readonly copyright: boolean
    protected readonly primaryOnly: boolean
    protected readonly timeout: number
    protected readonly retry: RetryOptions & { enabled?: boolean }
    protected readonly endpoints: Record<keyof TEndpoints, SenderEndpoint>

    protected requestId = 0

    public constructor(endpoints: TEndpoints, protected readonly primaryEndpoint: keyof TEndpoints, { headers = {}, timeout = 10_000, retry = true, copyright = false, primaryOnly = false, pool = {}, dns = {}, warmup = {} }: SenderOptions = {}) {
        super()

        this.warmupOptions = warmup
        this.headers = headers
        this.timeout = timeout
        this.retry = resolveNestedOptions(retry) || { enabled: false }
        this.copyright = copyright
        this.primaryOnly = primaryOnly
        this.endpoints = map(endpoints, (name, url) => [name, transform(parseUrl(url), ({ origin, path }) => ({ name, url, origin, path, pool: this.createPool(name, origin, pool, dns) }))]) as Record<keyof TEndpoints, SenderEndpoint>
    }

    public get isWarmupEnabled() {
        return this.warmupOptions.enabled ?? true
    }

    public isValidTipAmount(tip: bigint) {
        return tip >= this.minimumTipAmount
    }

    public appendSenderInstructions<TTransaction extends BaseTransactionMessage>(signer: TransactionSigner<Address>, tip: bigint, message: TTransaction) {
        return pipe(message, (message) => this.appendTipInstructions(signer, tip, message), (message) => this.appendIdentityInstructions(signer, message))
    }

    public getTipInstructions(source: TransactionSigner<Address>, tip: bigint) {
        if (this.tipWallets.length === 0 || tip === 0n) {
            return []
        }

        return [getTransferSolToRandomWalletInstruction(source, this.tipWallets, tip)]
    }

    public appendTipInstructions<TTransaction extends BaseTransactionMessage>(source: TransactionSigner<Address>, tip: bigint, message: TTransaction) {
        return appendTransactionMessageInstructions(this.getTipInstructions(source, tip), message)
    }

    public getIdentityInstructions(signer: TransactionSigner<Address>) {
        return this.copyright ? [this.getCopyrightInstruction(signer)] : []
    }

    public appendIdentityInstructions<TTransaction extends BaseTransactionMessage>(signer: TransactionSigner<Address>, message: TTransaction) {
        return appendTransactionMessageInstructions(this.getIdentityInstructions(signer), message)
    }

    public async startWarmupIfEnabled() {
        if (this.isWarmupEnabled) {
            await this.warmup().then(() => this.startWarmupInterval())
        }
    }

    public startWarmupInterval() {
        setInterval(() => this.warmup(true).catch((error) => this.emit('warmupFailed', error)), this.warmupOptions.interval ?? 30_000)
    }

    public async warmup(fromInterval = false) {
        const { connections = 1, path, method = 'OPTIONS', headers, body } = this.warmupOptions
        const promises: Array<Promise<unknown>> = []
        const connections_: Record<string, number> = {}

        this.emit('warmingUp', connections, fromInterval)

        for (const [name, endpoint] of Object.entries(this.endpoints)) {
            for (let i = 0; i < connections; i++) {
                promises.push(this.sendRequest(endpoint, { path, method, headers, body }).then(() => connections_[name] = endpoint.pool.stats.connected))
            }
        }

        await Promise.all(promises).then(() => this.emit('warmedUp', connections_, fromInterval))
    }

    public async sendToPrimaryEndpoint(serializedTransaction: Base64EncodedWireTransaction) {
        return this.send(this.endpoints[this.primaryEndpoint], this.buildSendRequest(serializedTransaction))
    }

    public async sendToAllEndpoints(serializedTransaction: Base64EncodedWireTransaction, waitAllCompleted = false) {
        if (this.primaryOnly) {
            return this.sendToPrimaryEndpoint(serializedTransaction)
        }

        const signature = createDeferred<Signature>()
        const request = this.buildSendRequest(serializedTransaction)
        const errors: unknown[] = []

        const handleResult = (result: Signature) => {
            if (!signature.isSettled) {
                signature.resolve(result)
            }
        }

        const handleError = (error: unknown) => {
            errors.push(error)
        }

        const promise = Promise.all(Object.values(this.endpoints).map(async (endpoint) => this.send(endpoint, request).then(handleResult, handleError))).then(() => {
            if (!signature.isSettled) {
                signature.reject(new AggregateError(errors, 'All send attempts failed'))
            }
        })

        if (waitAllCompleted) {
            await promise
        }

        return signature
    }

    protected abstract getSendRequestBody(serializedTransaction: Base64EncodedWireTransaction): any

    protected abstract getSignatureFromSendResult(result: unknown, createError: (message: string, options?: ErrorOptions) => Error): Signature

    protected getCopyrightInstruction(signer?: TransactionSigner<Address>) {
        return getAddMemoInstruction({ signers: notNullish(signer) ? [signer] : undefined, memo: `Sent by ${this.name}` })
    }

    protected async send(endpoint: SenderEndpoint, request: SendOptions & { requestId: number, took: bigint }) {
        this.emit('beforeRequest', endpoint.name, endpoint.pool.stats.free)

        const sendStart = process.hrtime.bigint()
        const response = await this.sendRequest(endpoint, request)
        const sendTook = process.hrtime.bigint() - sendStart
        const parseResponseStart = process.hrtime.bigint()
        const responseBody = await response.body.text()
        const parsed = tryCatch(() => JSON.parse(responseBody), null)
        const createError = (message: string, options?: ErrorOptions) => Object.assign(new Error(message, options), this.getResponseErrorContext(endpoint.name, request, response, responseBody))

        if (isNullish(parsed)) {
            throw createError('Failed to parse response')
        }

        const result = this.getSignatureFromSendResult(parsed, createError)

        if (notNullish(request.requestId) && notNullish(request.took)) {
            const parseResponseTook = process.hrtime.bigint() - parseResponseStart
            const stats: SendStats = { buildTime: request.took, sendTime: sendTook, parseResponseTime: parseResponseTook }

            this.emit('request', endpoint.name, request.requestId, stats)
        }

        return result
    }

    protected getResponseErrorContext(endpoint: string, request: SendOptions, response: Dispatcher.ResponseData, responseBody: string) {
        const request_ = pick(request, 'headers', 'body')
        const response_ = { status: response.statusCode, headers: response.headers, body: responseBody }

        return { endpoint, request: request_, response: response_ }
    }

    protected buildSendRequest(serializedTransaction: Base64EncodedWireTransaction): SendOptions & { requestId: number, took: bigint } {
        return transform(process.hrtime.bigint(), (start) => ({ method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(this.getSendRequestBody(serializedTransaction)), requestId: ++this.requestId, took: process.hrtime.bigint() - start }))
    }

    protected async sendRequest(endpoint: SenderEndpoint, { method = 'GET', headers: headers_ = {}, timeout = this.timeout, retry = {}, path, ...options }: SendOptions = {}) {
        const headers = { ...headers_, ...this.headers }
        const retryOptions = { enabled: true, ...this.retry, ...retry }
        const request = { url: endpoint.url, method, headers, body: options.body }
        const execute = async () => withTimeout(endpoint.pool.request({ method, blocking: false, origin: endpoint.origin, path: path ?? endpoint.path, headers, ...options }), timeout, () => new Error('Request timeout'))

        try {
            if (!retry.enabled) {
                return await execute()
            }

            return await withRetry(execute, { retries: retryOptions.retries, delay: retryOptions.delay, onFailedAttempt: (error) => tap(void 0, () => this.emit('retry', Object.assign(error, { request }))) })
        } catch (error) {
            throw Object.assign(new Error('Failed to send request', { cause: error }), request)
        }
    }

    protected createPool(name: string, origin: string, poolOptions: Pool.Options = {}, dnsOptions: interceptors.DNSInterceptorOpts & { enabled?: boolean } = {}) {
        const pool = new Pool(origin, poolOptions)

        pool.on('connect', () => this.emit('connections', name, pool.stats.connected))

        pool.on('disconnect', (_, __, error) => {
            console.log(error)
            this.emit(`connections`, name, pool.stats.connected)
        })

        if (!dnsOptions.enabled) {
            return pool
        }

        return pool.compose([interceptors.dns(dnsOptions)]) as Pool
    }
}
