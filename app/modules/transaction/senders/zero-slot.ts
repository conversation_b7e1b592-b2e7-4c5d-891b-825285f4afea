import type { SenderOptions, WarmupOptions } from './sender'
import { createRequestMessage, type UrlLike } from '@kdt310722/rpc'
import { ZERO_SLOT_ENDPOINTS, ZERO_SLOT_MINIMUM_TIP_AMOUNT, ZERO_SLOT_TIP_ACCOUNTS } from '../constants'
import { RpcSender } from './rpc'
import { formatEndpoints } from './temporal'

export interface ZeroSlotSenderOptions<TEndpoints extends Record<string, UrlLike> = typeof ZERO_SLOT_ENDPOINTS> extends SenderOptions {
    endpoints?: TEndpoints
}

export const DEFAULT_0SLOT_WARMUP_OPTIONS: WarmupOptions = {
    interval: 60_000,
    method: 'POST',
    body: JSON.stringify(createRequestMessage(1, 'getHealth')),
}

export class ZeroSlotSender<TEndpoints extends Record<string, UrlLike> = typeof ZERO_SLOT_ENDPOINTS> extends Rpc<PERSON><PERSON> {
    public override readonly name = '0Slot'
    public override readonly tipWallets = ZERO_SLOT_TIP_ACCOUNTS
    public override readonly minimumTipAmount = ZERO_SLOT_MINIMUM_TIP_AMOUNT

    public constructor(endpoint: Extract<keyof TEndpoints, string>, apiKey: string, { endpoints, warmup = {}, ...options }: ZeroSlotSenderOptions<TEndpoints> = {}) {
        super(formatEndpoints(endpoints ?? ZERO_SLOT_ENDPOINTS, apiKey, 'api-key'), endpoint, { ...options, warmup: { ...DEFAULT_0SLOT_WARMUP_OPTIONS, ...warmup } })
    }
}
