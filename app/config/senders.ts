import { notNullish } from '@kdt310722/utils/common'
import { omit } from '@kdt310722/utils/object'
import { z } from 'zod'
import { BLOX_ROUTE_ENDPOINTS, JITO_ENDPOINTS, NEXTBLOCK_ENDPOINTS, TEMPORAL_ENDPOINTS, ZERO_SLOT_ENDPOINTS } from '../modules/transaction/constants'
import { BloxRouteSender } from '../modules/transaction/senders/bloxroute'
import { JitoSender } from '../modules/transaction/senders/jito'
import { NextBlockSender } from '../modules/transaction/senders/nextblock'
import { RpcSender } from '../modules/transaction/senders/rpc'
import { TemporalSender } from '../modules/transaction/senders/temporal'
import { ZeroSlotSender } from '../modules/transaction/senders/zero-slot'
import { nullish } from '../utils/schemas/nullish'
import { retry } from '../utils/schemas/retry'
import { httpUrl } from '../utils/schemas/urls'

const timeout = z.number().positive()

const pool = z.object({
    connections: z.number().int().positive().nullish().default(null),
    clientTtl: timeout.nullish().default(null),
    pipelining: z.number().int().nonnegative().default(1),
    autoSelectFamily: z.boolean().optional(),
    autoSelectFamilyAttemptTimeout: timeout.optional(),
    allowH2: z.boolean().default(true),
    maxConcurrentStreams: z.number().int().positive().optional(),
})

const dns = z.object({
    enabled: z.boolean().default(true),
    maxTTL: timeout.default(60 * 60 * 60 * 1000),
    maxItems: z.number().positive().default(Number.POSITIVE_INFINITY),
})

const warmup = z.object({
    enabled: z.boolean().default(true),
    connections: z.number().int().positive().default(3),
    interval: z.number().positive().default(60_000),
    method: z.string().default('OPTIONS'),
    headers: z.record(z.string(), z.string()).default({}),
    body: z.string().nullish(),
})

const base = z.object({
    enabled: z.boolean().default(true),
    retry,
    timeout: timeout.default(3000),
    headers: nullish(z.record(z.string(), z.string())),
    copyright: z.boolean().default(false),
    pool: pool.default({}),
    dns: dns.default({}),
    warmup: warmup.default({}),
})

const rpc = base.extend({ type: z.literal('rpc'), endpoint: httpUrl })
const jito = base.extend({ type: z.literal('jito'), endpoint: z.enum(Object.keys(JITO_ENDPOINTS) as [keyof typeof JITO_ENDPOINTS, ...Array<keyof typeof JITO_ENDPOINTS>]).default('mainnet') })
const nextblock = base.extend({ type: z.literal('nextblock'), apiKey: z.string(), endpoint: z.enum(Object.keys(NEXTBLOCK_ENDPOINTS) as [keyof typeof NEXTBLOCK_ENDPOINTS, ...Array<keyof typeof NEXTBLOCK_ENDPOINTS>]) })
const bloxroute = base.extend({ type: z.literal('bloxroute'), apiKey: z.string(), endpoint: z.enum(Object.keys(BLOX_ROUTE_ENDPOINTS) as [keyof typeof BLOX_ROUTE_ENDPOINTS, ...Array<keyof typeof BLOX_ROUTE_ENDPOINTS>]) })
const temporal = base.extend({ type: z.literal('temporal'), apiKey: z.string(), endpoint: z.enum(Object.keys(TEMPORAL_ENDPOINTS) as [keyof typeof TEMPORAL_ENDPOINTS, ...Array<keyof typeof TEMPORAL_ENDPOINTS>]) })
const zeroSlot = base.extend({ type: z.literal('0slot'), apiKey: z.string(), endpoint: z.enum(Object.keys(ZERO_SLOT_ENDPOINTS) as [keyof typeof ZERO_SLOT_ENDPOINTS, ...Array<keyof typeof ZERO_SLOT_ENDPOINTS>]) })

const sender = z.discriminatedUnion('type', [rpc, jito, nextblock, bloxroute, temporal, zeroSlot]).transform((value) => {
    if (!value.enabled) {
        return null
    }

    const options = omit(value, 'type', 'endpoint', 'enabled')

    switch (value.type) {
        case 'rpc':
            return new RpcSender({ default: value.endpoint }, 'default', options)
        case 'jito':
            return new JitoSender(value.endpoint, options)
        case 'nextblock':
            return new NextBlockSender(value.endpoint, value.apiKey, options)
        case 'bloxroute':
            return new BloxRouteSender(value.endpoint, value.apiKey, options)
        case 'temporal':
            return new TemporalSender(value.endpoint, value.apiKey, options)
        case '0slot':
            return new ZeroSlotSender(value.endpoint, value.apiKey, options)
        default:
            throw new Error(`Invalid sender type: ${value['type']}`)
    }
})

export const senders = z.array(sender).nonempty().readonly().transform((val) => val.filter(notNullish))
